// eslint-disable-next-line @typescript-eslint/no-require-imports
const fs = require("fs");
// eslint-disable-next-line @typescript-eslint/no-require-imports
const path = require("path");
// eslint-disable-next-line @typescript-eslint/no-require-imports
const parser = require("typescript-react-intl").default;
const outDir = "src/i18n/locales";

function findFiles(dir, extensions = [".ts", ".js"]) {
  let results = [];
  const files = fs.readdirSync(dir);

  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);

    if (stat.isDirectory()) {
      results = results.concat(findFiles(filePath, extensions));
    } else if (extensions.some((ext) => file.endsWith(ext))) {
      results.push(filePath);
    }
  }

  return results;
}

function runner(callback) {
  let results = [];
  try {
    const files = findFiles("src", [".ts", ".js"]);

    files.forEach((file) => {
      const contents = fs.readFileSync(file).toString();
      const result = parser(contents);
      results = results.concat(result);
    });

    callback(results);
  } catch (error) {
    throw new Error(error);
  }
}

const pasteEmptyKeys = (data, englishLocale) => {
  Object.keys(englishLocale).forEach((key) => {
    if (!data[key]) {
      data[key] = "";
    }
  });

  return data;
};

const deleteUnusedKeys = (englishLocale, currentLocale) => {
  Object.keys(currentLocale).forEach((key) => {
    if (!englishLocale[key]) {
      delete currentLocale[key];
    }
  });

  return currentLocale;
};

runner((results) => {
  const englishLocale = JSON.parse(fs.readFileSync(`${outDir}/en.json`));
  const uaData = JSON.parse(fs.readFileSync(`${outDir}/uk.json`));

  Object.values(englishLocale).forEach((text, index) => {
    if (Object.values(englishLocale).indexOf(text) !== index) {
      console.log(text);
    }
  });

  results.forEach((result) => {
    englishLocale[result.id] =
      result.defaultMessage || englishLocale[result.id] || "";
  });

  const uaLocale = deleteUnusedKeys(
    englishLocale,
    pasteEmptyKeys(uaData, englishLocale)
  );

  const orderedEn = Object.keys(englishLocale)
    .sort()
    .reduce((obj, key) => {
      obj[key] = englishLocale[key];
      return obj;
    }, {});

  const orderedUa = Object.keys(uaLocale)
    .sort()
    .reduce((obj, key) => {
      obj[key] = uaLocale[key];
      return obj;
    }, {});

  fs.writeFileSync(`${outDir}/en.json`, JSON.stringify(orderedEn, null, 2));
  fs.writeFileSync(`${outDir}/uk.json`, JSON.stringify(orderedUa, null, 2));
});
