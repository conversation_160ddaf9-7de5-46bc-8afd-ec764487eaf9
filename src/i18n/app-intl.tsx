"use client";

import { AppLocale, useRootContext } from "@/context/root-context";
import { type PropsWithChildren } from "react";
import { IntlProvider } from "react-intl";

export type AppIntlProps = {
  enMessages: Record<string, string>;
  uaMessages: Record<string, string>;
};

export const AppIntl = ({
  enMessages,
  uaMessages,
  children,
}: PropsWithChildren<AppIntlProps>) => {
  const { locale } = useRootContext();
  const messages = {
    [AppLocale.en]: enMessages,
    [AppLocale.uk]: uaMessages,
  };

  return (
    <IntlProvider
      {...{
        locale: locale,
        // @ts-expect-error note
        messages: messages[locale],
      }}
    >
      {children}
    </IntlProvider>
  );
};
