import { ImageResponse } from 'next/og'

export const runtime = 'edge'

export const alt = '<PERSON> - Professional Boxing Coach'
export const size = {
  width: 1200,
  height: 630,
}
export const contentType = 'image/png'

export default async function Image() {
  return new ImageResponse(
    (
      <div
        style={{
          background: 'linear-gradient(135deg, #000000 0%, #ea580c 100%)',
          width: '100%',
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          fontFamily: 'system-ui',
          color: 'white',
          position: 'relative',
        }}
      >
        {/* Background Pattern */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'radial-gradient(circle at 30% 20%, rgba(234, 88, 12, 0.3) 0%, transparent 50%)',
          }}
        />
        
        {/* Main Content */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            textAlign: 'center',
            zIndex: 1,
          }}
        >
          {/* Boxing Gloves */}
          <div style={{ fontSize: '120px', marginBottom: '20px' }}>🥊</div>
          
          {/* Title */}
          <h1
            style={{
              fontSize: '72px',
              fontWeight: 'bold',
              margin: '0 0 20px 0',
              background: 'linear-gradient(90deg, #ffffff 0%, #fed7aa 100%)',
              backgroundClip: 'text',
              color: 'transparent',
            }}
          >
            Ivan Dudyak
          </h1>
          
          {/* Subtitle */}
          <p
            style={{
              fontSize: '36px',
              margin: '0 0 30px 0',
              color: '#fed7aa',
              fontWeight: '600',
            }}
          >
            Professional Boxing Coach
          </p>
          
          {/* Credentials */}
          <div
            style={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '10px',
            }}
          >
            <p style={{ fontSize: '24px', margin: 0, color: '#ffffff' }}>
              Master of Sport in Kickboxing
            </p>
            <p style={{ fontSize: '24px', margin: 0, color: '#ffffff' }}>
              17+ Years Experience • Kyiv, Ukraine
            </p>
            <p style={{ fontSize: '24px', margin: 0, color: '#ffffff' }}>
              Multiple Ukrainian Champion
            </p>
          </div>
        </div>
        
        {/* Bottom Badge */}
        <div
          style={{
            position: 'absolute',
            bottom: '40px',
            right: '40px',
            background: 'rgba(234, 88, 12, 0.9)',
            padding: '15px 25px',
            borderRadius: '25px',
            fontSize: '20px',
            fontWeight: 'bold',
          }}
        >
          Yes, You Can! 💪
        </div>
      </div>
    ),
    {
      ...size,
    }
  )
}
