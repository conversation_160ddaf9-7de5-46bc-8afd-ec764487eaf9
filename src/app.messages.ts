import { defineMessages } from "react-intl";

const appMessages = defineMessages({
  heroTitleTrain: {
    id: "app.hero-title-train",
    defaultMessage: "Train",
  },
  heroTitleLike: {
    id: "app.hero-title-like",
    defaultMessage: "Like",
  },
  heroTitleChampion: {
    id: "app.coachName",
    defaultMessage: "Champion",
  },
  heroCta: {
    id: "app.heroCta",
    defaultMessage: "Start",
  },

  logo: {
    id: "app.logo",
    defaultMessage: "Boxing Coach",
  },

  // Navigation
  navHome: {
    id: "app.navHome",
    defaultMessage: "Home",
  },
  navGallery: {
    id: "app.navGallery",
    defaultMessage: "Gallery",
  },
  navAbout: {
    id: "app.navAbout",
    defaultMessage: "About",
  },
  navComments: {
    id: "app.navComments",
    defaultMessage: "Comments",
  },
  navPricing: {
    id: "app.navPricing",
    defaultMessage: "Pricing",
  },
  navTrainNow: {
    id: "app.navTrainNow",
    defaultMessage: "Train Now",
  },

  // Gallery Section
  galleryTitle: {
    id: "app.galleryTitle",
    defaultMessage: "Training Gallery",
  },
  gallerySubtitle: {
    id: "app.gallerySubtitle",
    defaultMessage: "See our boxing training in action",
  },

  // About Ivan
  aboutTitle: {
    id: "app.aboutTitle",
    defaultMessage: "Meet Your Coach",
  },
  aboutCoachName: {
    id: "app.aboutCoachName",
    defaultMessage: "Ivan Dudyak",
  },
  aboutSubtitle: {
    id: "app.aboutSubtitle",
    defaultMessage: "Your Professional Boxing Coach",
  },
  aboutDescription: {
    id: "app.aboutDescription",
    defaultMessage:
      "Master of Sport in Kickboxing with 17 years of experience training fighters at all levels",
  },

  aboutMasterSport: {
    id: "about.masterSport",
    defaultMessage: "Master of Sport of Ukraine",
  },
  aboutMasterSportDesc: {
    id: "about.masterSportDesc",
    defaultMessage:
      "Master of Sport of Ukraine in kickboxing, first rank in boxing. Multiple champion of Ukraine.",
  },
  aboutEducation: {
    id: "about.education",
    defaultMessage: "Higher Education",
  },
  aboutEducationDesc: {
    id: "about.educationDesc",
    defaultMessage:
      "Higher pedagogical education (KhNPU), master's degree from Kharkiv Academy of Physical Culture.",
  },
  aboutChampion: {
    id: "about.champion",
    defaultMessage: "Champion of Ukraine",
  },
  aboutChampionDesc: {
    id: "about.championDesc",
    defaultMessage:
      "Multiple champion of the Championship of Ukraine, multiple champion of the Cups of Ukraine, prize-winner of the CIS Championship.",
  },
  aboutSpecialization: {
    id: "about.specialization",
    defaultMessage: "Specialization",
  },
  aboutSpecializationDesc: {
    id: "about.specializationDesc",
    defaultMessage:
      "Kickboxing, K-1 and boxing - beginner level, professionals, special physical training.",
  },

  // Comments Section
  commentsTitle: {
    id: "app.commentsTitle",
    defaultMessage: "What Our Students Say",
  },
  commentsSubtitle: {
    id: "app.commentsSubtitle",
    defaultMessage: "Follow our training journey on Instagram",
  },
  commentsFollowInstagram: {
    id: "app.commentsFollowInstagram",
    defaultMessage: "Follow on Instagram",
  },

  // Pricing
  pricingTitle: {
    id: "app.pricingTitle",
    defaultMessage: "Training Programs",
  },
  pricingSubtitle: {
    id: "app.pricingSubtitle",
    defaultMessage: "Choose the perfect training program for your goals",
  },
  pricingStarterFocusName: {
    id: "app.pricingStarterFocusName",
    defaultMessage: "Starter Focus",
  },
  pricingStarterFocusDuration: {
    id: "app.pricingStarterFocusDuration",
    defaultMessage: "4 weeks",
  },
  pricingStarterFocusPrice: {
    id: "app.pricingStarterFocusPrice",
    defaultMessage: "₴4,800",
  },
  pricingStarterFocusDescription: {
    id: "app.pricingStarterFocusDescription",
    defaultMessage: "Best for a specific goal or quick momentum.",
  },
  pricingStarterFocusFeature1: {
    id: "pricing.starterFocus.feature1",
    defaultMessage:
      "1 × diagnostic call (45–60 min) to define your goal & metrics of success",
  },
  pricingStarterFocusFeature2: {
    id: "pricing.starterFocus.feature2",
    defaultMessage: "3 × 60-min 1:1 coaching sessions (weekly)",
  },
  pricingStarterFocusFeature3: {
    id: "pricing.starterFocus.feature3",
    defaultMessage: "Session summaries + homework after each call",
  },
  pricingStarterFocusFeature4: {
    id: "pricing.starterFocus.feature4",
    defaultMessage:
      "Light chat support (Mon–Fri, 10:00–18:00, response within 24h)",
  },
  pricingStarterFocusFeature5: {
    id: "pricing.starterFocus.feature5",
    defaultMessage: "Rescheduling up to 24h before the session",
  },
  pricingStarterFocusFeature: {
    id: "pricing.starterFocus.feature",
    defaultMessage: "Feature", // Please update this defaultMessage to match your actual en.json value if needed
  },
  pricingDeepWorkName: {
    id: "app.pricingDeepWorkName",
    defaultMessage: "Deep Work",
  },
  pricingDeepWorkDuration: {
    id: "app.pricingDeepWorkDuration",
    defaultMessage: "8–10 weeks",
  },
  pricingDeepWorkPrice: {
    id: "app.pricingDeepWorkPrice",
    defaultMessage: "₴12,000",
  },
  pricingDeepWorkDescription: {
    id: "app.pricingDeepWorkDescription",
    defaultMessage: "For meaningful change with accountability.",
  },
  pricingDeepWorkFeature1: {
    id: "pricing.deepWork.feature1",
    defaultMessage: "8 × 60-min 1:1 sessions (weekly/bi-weekly)",
  },
  pricingDeepWorkFeature2: {
    id: "pricing.deepWork.feature2",
    defaultMessage: "360° goal map + personal development plan",
  },
  pricingDeepWorkFeature3: {
    id: "pricing.deepWork.feature3",
    defaultMessage: "Between-session support (voice notes or chat, Mon–Fri)",
  },
  pricingDeepWorkFeature4: {
    id: "pricing.deepWork.feature4",
    defaultMessage:
      "Templates & tools (habits, weekly review, OKR-style tracking)",
  },
  pricingDeepWorkFeature5: {
    id: "pricing.deepWork.feature5",
    defaultMessage:
      "Optional stakeholder check-in (if relevant to work/career)",
  },
  pricingDeepWorkFeature6: {
    id: "pricing.deepWork.feature6",
    defaultMessage:
      "Priority scheduling + roll-over of 1 missed session (with notice)",
  },
  pricingSingleSession: {
    id: "app.pricingSingleSession",
    defaultMessage: "Single session: ₴1,800 (60 min)",
  },
  pricingCorporateWorkshop: {
    id: "app.pricingCorporateWorkshop",
    defaultMessage:
      "Corporate/Team workshop: from ₴15,000 (half-day) depending on scope",
  },

  // Gallery
  galleryCard1Title: {
    id: "app.galleryCard1Title",
    defaultMessage: "Heavy Bag Training",
  },
  galleryCard1Category: {
    id: "app.galleryCard1Category",
    defaultMessage: "Training",
  },
  galleryCard1Description: {
    id: "app.galleryCard1Description",
    defaultMessage:
      "Master the fundamentals of boxing with intensive heavy bag training. Develop power, technique, and endurance under professional guidance.",
  },
  galleryCard2Title: {
    id: "app.galleryCard2Title",
    defaultMessage: "Pad Work Sessions",
  },
  galleryCard2Category: {
    id: "app.galleryCard2Category",
    defaultMessage: "Technique",
  },
  galleryCard2Description: {
    id: "app.galleryCard2Description",
    defaultMessage:
      "Precision training with focus mitts to improve accuracy, timing, and combination work. Essential for developing boxing skills.",
  },
  galleryCard3Title: {
    id: "app.galleryCard3Title",
    defaultMessage: "Sparring Practice",
  },
  galleryCard3Category: {
    id: "app.galleryCard3Category",
    defaultMessage: "Combat",
  },
  galleryCard3Description: {
    id: "app.galleryCard3Description",
    defaultMessage:
      "Controlled sparring sessions to apply techniques in real combat situations. Build confidence and fighting instincts.",
  },
  galleryCard4Title: {
    id: "app.galleryCard4Title",
    defaultMessage: "Strength & Conditioning",
  },
  galleryCard4Category: {
    id: "app.galleryCard4Category",
    defaultMessage: "Fitness",
  },
  galleryCard4Description: {
    id: "app.galleryCard4Description",
    defaultMessage:
      "Specialized conditioning programs to build the strength, speed, and endurance required for competitive boxing.",
  },
  galleryCard5Title: {
    id: "app.galleryCard5Title",
    defaultMessage: "Competition Preparation",
  },
  galleryCard5Category: {
    id: "app.galleryCard5Category",
    defaultMessage: "Professional",
  },
  galleryCard5Description: {
    id: "app.galleryCard5Description",
    defaultMessage:
      "Advanced training for competitive fighters. Strategic preparation for tournaments and professional bouts.",
  },
  galleryCard6Title: {
    id: "app.galleryCard6Title",
    defaultMessage: "Youth Training",
  },
  galleryCard6Category: {
    id: "app.galleryCard6Category",
    defaultMessage: "Youth",
  },
  galleryCard6Description: {
    id: "app.galleryCard6Description",
    defaultMessage:
      "Safe and engaging boxing training for young athletes. Building discipline, confidence, and fundamental skills.",
  },

  // Pricing Button
  pricingGetStarted: {
    id: "app.pricingGetStarted",
    defaultMessage: "Get Started",
  },

  // FAQ
  faqTitle: {
    id: "app.faqTitle",
    defaultMessage: "We're often asked",
  },
  faqQ1Question: {
    id: "app.faqQ1Question",
    defaultMessage: "What's the difference between coaching and therapy?",
  },
  faqQ1Answer: {
    id: "app.faqQ1Answer",
    defaultMessage:
      "Therapy explores past patterns and mental health; coaching focuses on present-to-future goals, behavior change, and performance. I don't diagnose or treat—my role is to help you get clarity, strategy, and action.",
  },
  faqQ2Question: {
    id: "app.faqQ2Question",
    defaultMessage: "How many sessions do I need and how long is each one?",
  },
  faqQ2Answer: {
    id: "app.faqQ2Answer",
    defaultMessage:
      "Most clients choose 4–8 sessions (60 minutes each), weekly or bi-weekly. Engagements typically last a few weeks to a few months, depending on your goal.",
  },
  faqQ3Question: {
    id: "app.faqQ3Question",
    defaultMessage: "Is it confidential?",
  },
  faqQ3Answer: {
    id: "app.faqQ3Answer",
    defaultMessage:
      "Yes—confidentiality is core to coaching. While coaching isn't a legally protected relationship like doctor-patient, I follow professional ethics and keep your information private; we can also sign an NDA if needed.",
  },
  faqQ4Question: {
    id: "app.faqQ4Question",
    defaultMessage: "What happens in a session?",
  },
  faqQ4Answer: {
    id: "app.faqQ4Answer",
    defaultMessage:
      "We clarify your goal, explore options, decide next actions, and set measurable checkpoints. You'll leave with a summary and small experiments (homework) to try before the next call.",
  },
  faqQ5Question: {
    id: "app.faqQ5Question",
    defaultMessage: "Do you give advice or tell me what to do?",
  },
  faqQ5Answer: {
    id: "app.faqQ5Answer",
    defaultMessage:
      "Coaching is collaborative: I ask high-leverage questions and offer frameworks; you decide. It's distinct from training/consulting, where an expert prescribes steps.",
  },
  faqQ6Question: {
    id: "app.faqQ6Question",
    defaultMessage: "How do we meet?",
  },
  faqQ6Answer: {
    id: "app.faqQ6Answer",
    defaultMessage:
      "Online (Zoom/Google Meet/Telegram). In-person available in Kyiv by arrangement.",
  },
  faqQ7Question: {
    id: "app.faqQ7Question",
    defaultMessage: "What if I miss or need to reschedule?",
  },
  faqQ7Answer: {
    id: "app.faqQ7Answer",
    defaultMessage:
      "You can reschedule up to 24 hours in advance at no charge; late cancellations/no-shows are counted as a session.",
  },
  faqQ8Question: {
    id: "app.faqQ8Question",
    defaultMessage: "Will I get results?",
  },
  faqQ8Answer: {
    id: "app.faqQ8Answer",
    defaultMessage:
      "Clients typically see progress when they attend sessions, complete actions between calls, and track metrics we define together. No guarantees—your outcomes depend on effort and fit.",
  },
  faqQ9Question: {
    id: "app.faqQ9Question",
    defaultMessage: "Do you offer support between sessions?",
  },
  faqQ9Answer: {
    id: "app.faqQ9Answer",
    defaultMessage:
      "Yes—Starter includes light chat support; Deep Work includes priority chat/voice notes on weekdays.",
  },
  faqQ10Question: {
    id: "app.faqQ10Question",
    defaultMessage: "What languages do you work in?",
  },
  faqQ10Answer: {
    id: "app.faqQ10Answer",
    defaultMessage: "Ukrainian / English (and Russian upon request).",
  },
  faqQ11Question: {
    id: "app.faqQ11Question",
    defaultMessage: "How do payments work?",
  },
  faqQ11Answer: {
    id: "app.faqQ11Answer",
    defaultMessage:
      "Prepay via card/IBAN. Invoices available for business clients.",
  },
  faqQ12Question: {
    id: "app.faqQ12Question",
    defaultMessage: "Do you work with companies/executives?",
  },
  faqQ12Answer: {
    id: "app.faqQ12Answer",
    defaultMessage:
      "Yes—Deep Work can include stakeholder check-ins and goal alignment when appropriate.",
  },

  // Contact
  contactTitle: {
    id: "app.contactTitle",
    defaultMessage: "Contact Us",
  },
  contactSubtitle: {
    id: "app.contactSubtitle",
    defaultMessage: "Ready to start your boxing journey?",
  },
  contactContactIvan: {
    id: "app.contactContactIvan",
    defaultMessage: "Contact Ivan",
  },
  contactDescription: {
    id: "app.contactDescription",
    defaultMessage:
      "Ready to start your boxing journey? Get in touch with Ivan today!",
  },
  contactCallIvan: {
    id: "app.contactCallIvan",
    defaultMessage: "Call Ivan",
  },
  contactTelegram: {
    id: "app.contactTelegram",
    defaultMessage: "Telegram",
  },
  contactEmail: {
    id: "app.contactEmail",
    defaultMessage: "Email",
  },
  contactTrainingLocation: {
    id: "app.contactTrainingLocation",
    defaultMessage: "Training Location",
  },
  contactLocation: {
    id: "app.contactLocation",
    defaultMessage: "Kyiv, Ukraine",
  },
  contactExperience: {
    id: "app.contactExperience",
    defaultMessage: "Master of Sport in Kickboxing • 17+ Years Experience",
  },

  // Common
  commonLoading: {
    id: "app.commonLoading",
    defaultMessage: "Loading...",
  },
  commonError: {
    id: "app.commonError",
    defaultMessage: "Error",
  },
  commonSuccess: {
    id: "app.commonSuccess",
    defaultMessage: "Success",
  },
  commonClose: {
    id: "app.commonClose",
    defaultMessage: "Close",
  },
  commonSubmit: {
    id: "app.commonSubmit",
    defaultMessage: "Submit",
  },
  commonCancel: {
    id: "app.commonCancel",
    defaultMessage: "Cancel",
  },

  // Footer
  footerMotivation: {
    id: "footer.motivation",
    defaultMessage: "Yes, You Can!",
  },
  footerSubtitle: {
    id: "footer.subtitle",
    defaultMessage: "Every champion was once a beginner who refused to give up",
  },
  footerQuote: {
    id: "footer.quote",
    defaultMessage:
      '"The fight is won or lost far away from witnesses - behind the lines, in the gym, and out there on the road, long before I dance under those lights."',
  },
  footerQuoteAuthor: {
    id: "footer.quoteAuthor",
    defaultMessage: "Muhammad Ali",
  },
  footerContact: {
    id: "footer.contact",
    defaultMessage: "Ready to Start Your Journey?",
  },
  footerLocation: {
    id: "footer.location",
    defaultMessage: "Training Location: Kyiv, Ukraine",
  },
  footerPhone: {
    id: "footer.phone",
    defaultMessage: "Phone",
  },
  footerEmail: {
    id: "footer.email",
    defaultMessage: "Email",
  },
  footerTelegram: {
    id: "footer.telegram",
    defaultMessage: "Telegram",
  },
  footerInstagram: {
    id: "footer.instagram",
    defaultMessage: "Follow on Instagram",
  },
  footerCopyright: {
    id: "footer.copyright",
    defaultMessage: "© 2025 Ivan Dudyak Boxing Academy. All rights reserved.",
  },
  footerMadeWith: {
    id: "footer.madeWith",
    defaultMessage: "Made with ❤️ for champions",
  },
});

export default appMessages;
