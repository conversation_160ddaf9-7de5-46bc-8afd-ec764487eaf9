"use client";

import { createContext, useState, ReactNode, useContext } from "react";

type RootContextType = {
  locale: string;
  setLocale: (locale: AppLocale) => void;
};

export enum AppLocale {
  en = "en",
  uk = "uk",
}

export const RootContext = createContext<RootContextType>({
  locale: AppLocale.uk,
  setLocale: () => {},
});

export const RootProvider = ({ children }: { children: ReactNode }) => {
  const [locale, setLocale] = useState(AppLocale.uk);

  return (
    <RootContext.Provider value={{ locale, setLocale }}>
      {children}
    </RootContext.Provider>
  );
};

export const useRootContext = () => {
  return useContext(RootContext);
};
