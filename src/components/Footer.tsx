"use client";

import React from "react";
import { useIntl } from "react-intl";
import { Phone, Mail, MapPin, Instagram, MessageCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import ContactModal from "./ContactModal";
import appMessages from "@/app.messages";

const BoxingGlovesMarquee = () => {
  const gloves = "🥊";
  const marqueeContent = Array(20).fill(gloves).join("  ");
  
  return (
    <div className="relative overflow-hidden bg-gradient-to-r from-orange-600 to-orange-700 py-4">
      <div className="animate-marquee whitespace-nowrap">
        <span className="text-4xl mx-4">{marqueeContent}</span>
        <span className="text-4xl mx-4">{marqueeContent}</span>
      </div>
    </div>
  );
};

const Footer = () => {
  const { formatMessage: t } = useIntl();

  return (
    <footer className="relative bg-black text-white">
      {/* Boxing Gloves Marquee */}
      <BoxingGlovesMarquee />
      
      {/* Main Footer Content */}
      <div className="relative">
        {/* Background gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-orange-900/20" />
        
        <div className="relative z-10 max-w-7xl mx-auto px-6 py-16">
          {/* Motivational Section */}
          <div className="text-center mb-16">
            <div className="mb-8">
              <h2 className="text-6xl md:text-8xl font-black text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600 mb-4">
                {t(appMessages.footerMotivation)}
              </h2>
              <p className="text-xl md:text-2xl text-gray-300 font-medium">
                {t(appMessages.footerSubtitle)}
              </p>
            </div>
            
            {/* Inspirational Quote */}
            <div className="max-w-4xl mx-auto mb-12">
              <blockquote className="text-lg md:text-xl text-gray-200 italic leading-relaxed mb-4">
                {t(appMessages.footerQuote)}
              </blockquote>
              <cite className="text-orange-400 font-semibold text-lg">
                — {t(appMessages.footerQuoteAuthor)}
              </cite>
            </div>
          </div>

          {/* Contact Section */}
          <div className="grid md:grid-cols-2 gap-12 mb-16">
            {/* Contact Info */}
            <div>
              <h3 className="text-3xl font-bold text-orange-400 mb-8">
                {t(appMessages.footerContact)}
              </h3>
              
              <div className="space-y-6">
                <div className="flex items-center space-x-4">
                  <MapPin className="w-6 h-6 text-orange-400 flex-shrink-0" />
                  <span className="text-lg text-gray-200">
                    {t(appMessages.footerLocation)}
                  </span>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Phone className="w-6 h-6 text-orange-400 flex-shrink-0" />
                  <a 
                    href="tel:+380123456789" 
                    className="text-lg text-gray-200 hover:text-orange-400 transition-colors"
                  >
                    +380 (12) 345-67-89
                  </a>
                </div>
                
                <div className="flex items-center space-x-4">
                  <Mail className="w-6 h-6 text-orange-400 flex-shrink-0" />
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-lg text-gray-200 hover:text-orange-400 transition-colors"
                  >
                    <EMAIL>
                  </a>
                </div>
                
                <div className="flex items-center space-x-4">
                  <MessageCircle className="w-6 h-6 text-orange-400 flex-shrink-0" />
                  <a 
                    href="https://t.me/ivan_boxing" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-lg text-gray-200 hover:text-orange-400 transition-colors"
                  >
                    @ivan_boxing
                  </a>
                </div>
              </div>
            </div>

            {/* Call to Action */}
            <div className="flex flex-col justify-center">
              <div className="text-center md:text-left">
                <h4 className="text-2xl font-bold text-white mb-6">
                  Start Your Champion Journey Today
                </h4>
                <p className="text-gray-300 mb-8 text-lg leading-relaxed">
                  Transform your life through the discipline and power of boxing. 
                  Join hundreds of students who have already started their journey to greatness.
                </p>
                
                <ContactModal>
                  <button className="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white font-bold py-4 px-8 rounded-xl text-lg transition-all duration-300 transform hover:scale-105 shadow-lg">
                    Begin Your Training
                  </button>
                </ContactModal>
              </div>
            </div>
          </div>

          {/* Social Media */}
          <div className="text-center mb-12">
            <a
              href="https://www.instagram.com/ivan.kick/"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center space-x-3 bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white px-6 py-3 rounded-full transition-all duration-300 transform hover:scale-105"
            >
              <Instagram className="w-5 h-5" />
              <span className="font-semibold">{t(appMessages.footerInstagram)}</span>
            </a>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-700 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <p className="text-gray-400 text-sm">
                {t(appMessages.footerCopyright)}
              </p>
              <p className="text-gray-400 text-sm">
                {t(appMessages.footerMadeWith)}
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
