"use client";

import appMessages from "@/app.messages";
import { useIntl } from "react-intl";

interface HeroSectionProps {
  videoSrc?: string;
}

export const HeroSection = ({ videoSrc }: HeroSectionProps) => {
  const { formatMessage: t } = useIntl();

  return (
    <section id="hero">
      <div className="h-full md:h-[900px] relative">
        {videoSrc && (
          <div className="absolute inset-0 w-full h-full overflow-hidden">
            <video
              autoPlay
              muted
              loop
              playsInline
              className="absolute inset-0 w-full h-full object-cover"
            >
              <source src={videoSrc} type="video/mp4" />
            </video>
            <div className="absolute inset-0 bg-black/30"></div>
          </div>
        )}

        <div className="relative z-10">
          <div className="h-full w-full relative md:z-0">
            <div className="hidden md:block absolute md:top-24 right-0">
              <div style={{ opacity: 1, transform: "none" }}>
                <div className="w-[600px] h-[600px] bg-gradient-to-br from-orange-500 to-orange-700 rounded-full opacity-20 animate-pulse"></div>
              </div>
            </div>

            <div className="hidden md:block absolute top-56 z-[9999] left-16">
              <div style={{ opacity: 1, transform: "none" }}>
                <h1 className="flex items-center font-semibold flex-col text-[12.5rem] pb-4 text-white">
                  <span>{t(appMessages.heroTitleTrain)}</span>
                  <span>{t(appMessages.heroTitleLike)}</span>
                </h1>
              </div>
            </div>

            <div className="md:hidden absolute top-[calc(100vh-400px)] z-[9999] w-full px-4">
              <div style={{ opacity: 1, transform: "none" }}>
                <h1 className="font-black text-4xl sm:text-5xl text-white leading-tight text-right">
                  <span>{t(appMessages.heroTitleTrain)}</span>
                  <br />
                  <span>{t(appMessages.heroTitleLike)}</span>
                </h1>
              </div>
            </div>

            <div className="absolute top-[calc(100vh-340px)] md:top-[calc(100vh-530px)] right-2 md:right-7">
              <h2 className="font-bold pt-9 text-5xl md:text-[12.5rem] text-orange-600">
                <span
                  className="inline-block mr-[0.25em] whitespace-nowrap"
                  aria-hidden="true"
                >
                  {t(appMessages.heroTitleChampion)
                    .split("")
                    .map((char: string, idx: number) => (
                      <span
                        key={idx}
                        aria-hidden="true"
                        className="inline-block -mr-[0.01em]"
                        style={{
                          opacity: 1,
                          transform: "translateY(0em) translateZ(0px)",
                        }}
                      >
                        {char}
                      </span>
                    ))}
                </span>
              </h2>
            </div>
          </div>

          <div className="md:hidden">
            <div
              style={{
                opacity: 0,
                transform: "translateY(24px) translateZ(0)",
              }}
            >
              <div className="relative h-screen max-h-[1000px] w-full min-h-[500px] lg:min-h-[600px] before:absolute before:inset-0 before:bg-orange-600 before:opacity-30 overflow-hidden">
                <div className="absolute inset-0 h-full w-full bg-gradient-to-br from-orange-500 to-orange-700 rounded-br-[88px]"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};
