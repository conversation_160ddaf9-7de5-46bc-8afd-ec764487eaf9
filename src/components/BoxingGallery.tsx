"use client";

import { useIntl } from "react-intl";
import { Carousel, Card } from "@/components/ui/apple-cards-carousel";
import appMessages from "../app.messages";

const BoxingGallery = () => {
  const { formatMessage: t } = useIntl();

  const data = [
    {
      category: t(appMessages.galleryCard1Category),
      title: t(appMessages.galleryCard1Title),
      src: "https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=800&h=600&fit=crop&crop=center",
      content: (
        <DummyContent
          imageSrc="https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=800&h=600&fit=crop&crop=center"
          title={t(appMessages.galleryCard1Title)}
          description={t(appMessages.galleryCard1Description)}
        />
      ),
    },
    {
      category: t(appMessages.galleryCard2Category),
      title: t(appMessages.galleryCard2Title),
      src: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center",
      content: (
        <DummyContent
          imageSrc="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=600&fit=crop&crop=center"
          title={t(appMessages.galleryCard2Title)}
          description={t(appMessages.galleryCard2Description)}
        />
      ),
    },
    {
      category: t(appMessages.galleryCard3Category),
      title: t(appMessages.galleryCard3Title),
      src: "https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&h=600&fit=crop&crop=center",
      content: (
        <DummyContent
          imageSrc="https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&h=600&fit=crop&crop=center"
          title={t(appMessages.galleryCard3Title)}
          description={t(appMessages.galleryCard3Description)}
        />
      ),
    },
    {
      category: t(appMessages.galleryCard4Category),
      title: t(appMessages.galleryCard4Title),
      src: "https://images.unsplash.com/photo-1517438476312-10d79c077509?w=800&h=600&fit=crop&crop=center",
      content: (
        <DummyContent
          imageSrc="https://images.unsplash.com/photo-1517438476312-10d79c077509?w=800&h=600&fit=crop&crop=center"
          title={t(appMessages.galleryCard4Title)}
          description={t(appMessages.galleryCard4Description)}
        />
      ),
    },
    {
      category: t(appMessages.galleryCard5Category),
      title: t(appMessages.galleryCard5Title),
      src: "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center",
      content: (
        <DummyContent
          imageSrc="https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=600&fit=crop&crop=center"
          title={t(appMessages.galleryCard5Title)}
          description={t(appMessages.galleryCard5Description)}
        />
      ),
    },
    {
      category: t(appMessages.galleryCard6Category),
      title: t(appMessages.galleryCard6Title),
      src: "https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=800&h=600&fit=crop&crop=center",
      content: (
        <DummyContent
          imageSrc="https://images.unsplash.com/photo-1549719386-74dfcbf7dbed?w=800&h=600&fit=crop&crop=center"
          title={t(appMessages.galleryCard6Title)}
          description={t(appMessages.galleryCard6Description)}
        />
      ),
    },
  ];

  const cards = data.map((card, index) => (
    <Card key={card.src} card={card} index={index} />
  ));

  return (
    <section
      id="gallery"
      className="w-full h-full py-20 bg-gradient-to-b from-neutral-50 to-neutral-100 dark:from-neutral-900 dark:to-neutral-800"
    >
      <div className="w-full">
        <div className="text-center mb-16 px-6">
          <h2 className="text-4xl md:text-6xl font-bold text-neutral-800 dark:text-neutral-200 mb-4">
            {t(appMessages.galleryTitle)}
          </h2>
          <p className="text-xl text-neutral-600 dark:text-neutral-400">
            {t(appMessages.gallerySubtitle)}
          </p>
        </div>
        <div className="w-full overflow-hidden">
          <Carousel items={cards} autoPlayInterval={10000} />
        </div>
      </div>
    </section>
  );
};

const DummyContent = ({
  imageSrc,
  title,
  description,
}: {
  imageSrc: string;
  title: string;
  description: string;
}) => {
  return (
    <div className="bg-[#F5F5F7] dark:bg-neutral-800 p-8 md:p-14 rounded-3xl mb-4">
      <p className="text-neutral-600 dark:text-neutral-400 text-base md:text-2xl font-sans max-w-3xl mx-auto">
        <span className="font-bold text-neutral-700 dark:text-neutral-200">
          {title}
        </span>{" "}
        {description}
      </p>
      <div className="mt-8">
        <img
          src={imageSrc}
          alt={title}
          height="500"
          width="500"
          className="md:w-1/2 md:h-1/2 h-full w-full mx-auto object-cover rounded-lg"
        />
      </div>
    </div>
  );
};

export default BoxingGallery;
